'use client'

import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import {
  useElevenVoicesQuery,
  type ElevenVoice,
} from '@/hooks/useElevenVoicesQuery'
import { useMediaQuery } from '@/hooks/use-media-query'
import { Play, Pause, Volume2, Mars, Venus, Check, Gem } from 'lucide-react'
import { usePlanLimits, useVoiceRegeneration } from '@/hooks/use-feature-gating'
import { useUpgradeModal } from '@/hooks/use-upgrade-modal'
import { useVideoStore } from '@/store/video-store'

// Pre-computed flag icon styles to prevent object recreation
const FLAG_ICON_STYLE = {
  width: 20,
  height: 14,
  display: 'inline-block',
} as const

// Stable flag className mapping to prevent dynamic string generation
const FLAG_CLASS_MAP: Record<string, string> = {
  american: 'fi fi-us',
  british: 'fi fi-gb',
  australian: 'fi fi-au',
  canadian: 'fi fi-ca',
  irish: 'fi fi-ie',
  swedish: 'fi fi-se',
  german: 'fi fi-de',
  french: 'fi fi-fr',
  spanish: 'fi fi-es',
  italian: 'fi fi-it',
  portuguese: 'fi fi-pt',
  dutch: 'fi fi-nl',
  indian: 'fi fi-in',
  chinese: 'fi fi-cn',
  japanese: 'fi fi-jp',
  korean: 'fi fi-kr',
  russian: 'fi fi-ru',
  arabic: 'fi fi-sa',
  polish: 'fi fi-pl',
  czech: 'fi fi-cz',
  hungarian: 'fi fi-hu',
  romanian: 'fi fi-ro',
  bulgarian: 'fi fi-bg',
  croatian: 'fi fi-hr',
  slovak: 'fi fi-sk',
  slovenian: 'fi fi-si',
  estonian: 'fi fi-ee',
  latvian: 'fi fi-lv',
  lithuanian: 'fi fi-lt',
  finnish: 'fi fi-fi',
  danish: 'fi fi-dk',
  norwegian: 'fi fi-no',
  icelandic: 'fi fi-is',
  greek: 'fi fi-gr',
  turkish: 'fi fi-tr',
  ukrainian: 'fi fi-ua',
  serbian: 'fi fi-rs',
  bosnian: 'fi fi-ba',
  albanian: 'fi fi-al',
  macedonian: 'fi fi-mk',
  welsh: 'fi fi-gb-wls',
  scottish: 'fi fi-gb-sct',
} as const

interface VoicePickerModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectVoice: (voice: ElevenVoice, applyToAll?: boolean) => void
  showApplyToAllScenes?: boolean
}

// Helper function to get flag className - moved outside component for stability
const getFlagClassName = (accent: string): string | null => {
  return (
    FLAG_CLASS_MAP[accent] ||
    (accent.length >= 2 ? `fi fi-${accent.slice(0, 2).toLowerCase()}` : null)
  )
}

// Helper function to check if a voice is premium/gated
const isVoiceGated = (voice: ElevenVoice): boolean => {
  // Check for premium indicators in the voice data
  // This could be based on category, specific voice IDs, or other indicators
  // For now, we'll use category as an indicator (professional voices are often premium)
  if (voice.category === 'professional' || voice.category === 'premium') {
    return true
  }

  // Check for specific premium voice names or patterns
  const premiumIndicators = [
    'professional',
    'premium',
    'pro',
    'studio',
    'broadcast',
  ]
  const voiceName = voice.name.toLowerCase()
  const voiceDescription = voice.description?.toLowerCase() || ''

  return premiumIndicators.some(
    indicator =>
      voiceName.includes(indicator) || voiceDescription.includes(indicator)
  )
}

// Helper function to deduplicate voices by name
const deduplicateVoices = (voices: ElevenVoice[]): ElevenVoice[] => {
  const seen = new Map<string, ElevenVoice>()

  voices.forEach(voice => {
    const existing = seen.get(voice.name)
    if (!existing) {
      seen.set(voice.name, voice)
    } else {
      // Keep the voice with more complete data (prefer one with category or description)
      if (voice.category && !existing.category) {
        seen.set(voice.name, voice)
      } else if (voice.description && !existing.description) {
        seen.set(voice.name, voice)
      }
    }
  })

  return Array.from(seen.values())
}

// Isolated Voice Card Component - Uses global audio state
const IsolatedVoiceCard = ({
  voice,
  onSelect,
  onPlay,
  isSelected = false,
  isPlaying = false,
  planName,
  onUpgradeClick,
}: {
  voice: ElevenVoice
  onSelect: (voice: ElevenVoice) => void
  onPlay: (voice: ElevenVoice) => void
  isSelected?: boolean
  isPlaying?: boolean
  planName: string
  onUpgradeClick: () => void
}) => {
  const isGated = isVoiceGated(voice)
  const canSelect = !isGated || planName !== 'free'

  const handleSelect = useCallback(() => {
    if (!canSelect) {
      onUpgradeClick()
      return
    }
    onSelect(voice)
  }, [voice, onSelect, canSelect, onUpgradeClick])

  const handlePlay = useCallback(() => {
    // Simply call the onPlay callback - audio management is handled globally
    onPlay(voice)
  }, [voice, onPlay])

  return (
    <div
      className={`
        voice-card-isolated relative p-4 rounded-lg border transition-all duration-200
        ${
          !canSelect
            ? 'cursor-not-allowed opacity-60 bg-muted border-muted-foreground/20'
            : 'cursor-pointer bg-card hover:bg-accent hover:text-accent-foreground hover:border-primary/30 hover:shadow-md'
        }
        ${isSelected ? 'border-primary bg-primary/5 shadow-md' : 'border-border shadow-sm'}
        ${isSelected && canSelect ? 'hover:bg-primary/10 hover:border-primary/50' : ''}
      `}
      onClick={handleSelect}
    >
      {/* Checkmark overlay when selected */}
      {isSelected && canSelect && (
        <div className='absolute top-2 right-2 bg-primary text-white rounded-full p-1 z-10'>
          <Check className='h-3 w-3' />
        </div>
      )}

      {/* Premium gem icon for gated voices */}
      {isGated && (
        <div className='absolute bottom-2 right-2 bg-gradient-to-r from-orange-600 to-orange-500 text-white rounded-full p-1 z-10'>
          <Gem className='h-3 w-3' />
        </div>
      )}

      {/* Voice Avatar and Play Button */}
      <div className='flex items-center gap-3 mb-3'>
        <div className='relative'>
          <button
            className={`
              w-12 h-12 rounded-full transition-colors duration-200 border-0 cursor-pointer flex items-center justify-center
              ${
                isPlaying
                  ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                  : 'bg-primary/30 hover:bg-primary text-muted-foreground hover:text-primary-foreground'
              }
            `}
            onClick={e => {
              e.stopPropagation()
              handlePlay()
            }}
          >
            {isPlaying ? (
              <Pause className='h-4 w-4' />
            ) : (
              <Play className='h-4 w-4' />
            )}
          </button>
        </div>

        <div className='flex-1 min-w-0'>
          <div className='flex items-center gap-2 mb-1'>
            <h3 className='font-semibold text-base truncate'>{voice.name}</h3>
            {voice.labels?.gender === 'male' && (
              <Mars className='h-4 w-4 text-blue-600 dark:text-blue-400 shrink-0' />
            )}
            {voice.labels?.gender === 'female' && (
              <Venus className='h-4 w-4 text-pink-600 dark:text-pink-400 shrink-0' />
            )}
          </div>
          <div className='text-sm text-muted-foreground'>
            {voice.labels?.age &&
              voice.labels?.use_case &&
              `${voice.labels.age.charAt(0).toUpperCase() + voice.labels.age.slice(1)} • ${voice.labels.use_case.charAt(0).toUpperCase() + voice.labels.use_case.slice(1)}`}
          </div>
        </div>
      </div>

      {/* Voice Tags/Pills */}
      <div className='flex flex-wrap gap-1.5'>
        {voice.labels?.accent && voice.labels.accent !== 'standard' && (
          <div className='flex items-center gap-1'>
            {/* Flag icon with stable className lookup */}
            {(() => {
              const flagClass = getFlagClassName(voice.labels.accent)
              return flagClass ? (
                <span className={flagClass} style={FLAG_ICON_STYLE} />
              ) : null
            })()}
            <span className='text-xs text-muted-foreground capitalize'>
              {voice.labels.accent}
            </span>
          </div>
        )}

        {voice.labels?.age && (
          <span className='text-xs px-2 py-0.5 bg-secondary text-secondary-foreground rounded capitalize'>
            {voice.labels.age.replace('_', ' ')}
          </span>
        )}

        {voice.labels?.use_case && (
          <span className='text-xs px-2 py-0.5 border border-border rounded capitalize'>
            {voice.labels.use_case.replace('_', ' ')}
          </span>
        )}
      </div>
    </div>
  )
}

// Isolated Filter Component - No external state dependencies
const IsolatedFilterSection = ({
  voices,
  onFilterChange,
}: {
  voices: ElevenVoice[]
  onFilterChange: (filteredVoices: ElevenVoice[]) => void
}) => {
  const [searchTerm, setSearchTerm] = useState('')

  const filteredVoices = useMemo(() => {
    if (!searchTerm) return voices

    return voices.filter(
      voice =>
        voice.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        voice.labels?.language
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        voice.labels?.gender
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        voice.labels?.accent
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        voice.labels?.age?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        voice.labels?.use_case?.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [voices, searchTerm])

  useEffect(() => {
    onFilterChange(filteredVoices)
  }, [filteredVoices, onFilterChange])

  return (
    <div className='space-y-2'>
      <div className='flex items-center justify-between mb-2'>
        <label className='text-xs font-medium text-muted-foreground'>
          Search Voices
        </label>
        <span className='text-xs px-2 py-1 border border-border rounded'>
          {filteredVoices.length}{' '}
          {filteredVoices.length === 1 ? 'voice' : 'voices'} found
        </span>
      </div>
      <input
        type='text'
        placeholder='Search by name, language, gender, accent...'
        value={searchTerm}
        onChange={e => setSearchTerm(e.target.value)}
        className='w-full px-3 py-2 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
      />
    </div>
  )
}

// Main Modal Component - Simplified architecture
export function VoicePickerModal({
  isOpen,
  onClose,
  onSelectVoice,
  showApplyToAllScenes = true,
}: VoicePickerModalProps) {
  const { data: voices = [], isLoading } = useElevenVoicesQuery()
  const [filteredVoices, setFilteredVoices] = useState<ElevenVoice[]>([])
  const [selectedVoice, setSelectedVoice] = useState<ElevenVoice | null>(null)
  const [applyToAllScenes, setApplyToAllScenes] = useState(false)
  const isDesktop = useMediaQuery('(min-width: 768px)')

  // Global audio management for the modal
  const currentAudioRef = useRef<HTMLAudioElement | null>(null)
  const [currentPlayingVoiceId, setCurrentPlayingVoiceId] = useState<
    string | null
  >(null)

  // Plan limits and upgrade modal
  const { planName } = usePlanLimits()
  const { openUpgradeModal } = useUpgradeModal()
  const { project } = useVideoStore()

  // Voice regeneration tracking
  const voiceRegeneration = useVoiceRegeneration(project?.projectId || '')

  // Global audio management functions
  const stopCurrentAudio = useCallback(() => {
    if (currentAudioRef.current) {
      currentAudioRef.current.pause()
      currentAudioRef.current = null
    }
    setCurrentPlayingVoiceId(null)
  }, [])

  const playVoiceAudio = useCallback(
    async (voice: ElevenVoice) => {
      // Stop any currently playing audio
      stopCurrentAudio()

      if (voice.preview_url) {
        const audio = new Audio(voice.preview_url)
        currentAudioRef.current = audio
        setCurrentPlayingVoiceId(voice.voice_id)

        audio.onended = () => {
          setCurrentPlayingVoiceId(null)
          currentAudioRef.current = null
        }
        audio.onerror = () => {
          setCurrentPlayingVoiceId(null)
          currentAudioRef.current = null
        }

        try {
          await audio.play()
        } catch (error) {
          console.error('Error playing voice preview:', error)
          setCurrentPlayingVoiceId(null)
          currentAudioRef.current = null
        }
      }
    },
    [stopCurrentAudio]
  )

  // Check if user can use "Apply to All Scenes" feature (Premium only)
  const canApplyToAllScenes = planName === 'premium'

  // Deduplicate voices when they load
  const deduplicatedVoices = useMemo(() => {
    return voices.length > 0 ? deduplicateVoices(voices) : []
  }, [voices])

  // Initialize filtered voices when voices load
  useEffect(() => {
    if (deduplicatedVoices.length > 0 && filteredVoices.length === 0) {
      setFilteredVoices(deduplicatedVoices)
    }
  }, [deduplicatedVoices, filteredVoices.length])

  // Stop audio when modal closes
  useEffect(() => {
    if (!isOpen) {
      stopCurrentAudio()
    }
  }, [isOpen, stopCurrentAudio])

  // Stable callbacks - no dependencies on changing state
  const handleVoiceSelect = useCallback((voice: ElevenVoice) => {
    setSelectedVoice(voice)
  }, [])

  const handleVoicePlay = useCallback(
    (voice: ElevenVoice) => {
      // Use global audio management to play voice
      playVoiceAudio(voice)
    },
    [playVoiceAudio]
  )

  const handleFilterChange = useCallback((newFilteredVoices: ElevenVoice[]) => {
    setFilteredVoices(newFilteredVoices)
  }, [])

  const handleConfirm = useCallback(async () => {
    if (!selectedVoice) return

    // Check voice regeneration limits before confirming
    if (!voiceRegeneration.allowed) {
      // Show upgrade modal instead of allowing selection
      openUpgradeModal(
        'voiceRegenerations',
        `You've reached your limit of ${voiceRegeneration.limit} voice regenerations per project. Upgrade to continue regenerating voices.`
      )
      return
    }

    // Simply update the voice settings and close modal
    // Let the scene level handle voice generation
    onSelectVoice(selectedVoice, applyToAllScenes)
    onClose()
  }, [
    selectedVoice,
    applyToAllScenes,
    onSelectVoice,
    onClose,
    voiceRegeneration,
    openUpgradeModal,
  ])

  const handleCancel = useCallback(() => {
    // Stop any playing audio when modal closes
    stopCurrentAudio()
    setSelectedVoice(null)
    onClose()
  }, [onClose, stopCurrentAudio])

  const handleUpgradeClick = useCallback(() => {
    openUpgradeModal(
      'voiceRegenerations',
      'Upgrade to access premium voices with professional quality and unique characteristics.'
    )
  }, [openUpgradeModal])

  const handleApplyToAllScenesUpgrade = useCallback(() => {
    openUpgradeModal(
      'videoPublishing',
      'Upgrade to Premium to apply voice changes to all scenes at once.'
    )
  }, [openUpgradeModal])

  // Responsive modal content with proper flex layout for small screens
  const modalContent = (
    <div className='flex flex-col h-full'>
      {/* Filter Section - Fixed at top */}
      <div className='flex-shrink-0 border-b pb-4 mb-4'>
        <IsolatedFilterSection
          voices={deduplicatedVoices}
          onFilterChange={handleFilterChange}
        />
      </div>

      {/* Voice List - Scrollable content area */}
      <div className='flex-1 min-h-0 overflow-y-auto mb-4'>
        {isLoading ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2' />
              <p className='text-sm text-muted-foreground'>Loading voices...</p>
            </div>
          </div>
        ) : filteredVoices.length === 0 ? (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <p className='text-sm font-medium mb-2'>No voices found</p>
              <p className='text-xs text-muted-foreground'>
                Try adjusting your search
              </p>
            </div>
          </div>
        ) : (
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4 pb-4'>
            {filteredVoices.map(voice => (
              <IsolatedVoiceCard
                key={voice.voice_id}
                voice={voice}
                onSelect={handleVoiceSelect}
                onPlay={handleVoicePlay}
                isSelected={selectedVoice?.voice_id === voice.voice_id}
                isPlaying={currentPlayingVoiceId === voice.voice_id}
                planName={planName}
                onUpgradeClick={handleUpgradeClick}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer - Fixed at bottom */}
      <div
        className={`flex-shrink-0 flex items-center pt-4 border-t ${showApplyToAllScenes ? 'justify-between' : 'justify-end'}`}
      >
        {showApplyToAllScenes && (
          <div className='flex items-center space-x-2'>
            <input
              type='checkbox'
              id='apply-to-all'
              checked={applyToAllScenes}
              onChange={e => {
                if (!canApplyToAllScenes) {
                  handleApplyToAllScenesUpgrade()
                  return
                }
                setApplyToAllScenes(e.target.checked)
              }}
              className={`rounded border-border ${!canApplyToAllScenes ? 'cursor-not-allowed opacity-60' : ''}`}
              disabled={!canApplyToAllScenes}
            />
            <label
              htmlFor='apply-to-all'
              className={`text-sm font-medium flex items-center gap-1 ${!canApplyToAllScenes ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
              onClick={() => {
                if (!canApplyToAllScenes) {
                  handleApplyToAllScenesUpgrade()
                }
              }}
            >
              Apply same voice to all scenes
              {!canApplyToAllScenes && (
                <Gem className='h-4 w-4 text-orange-500 ml-1' />
              )}
              {!canApplyToAllScenes && (
                <span className='text-xs font-semibold text-orange-600 dark:text-orange-400'>
                  PREMIUM
                </span>
              )}
            </label>
          </div>
        )}
        <div className='flex items-center gap-3'>
          <button
            onClick={handleCancel}
            className='px-4 py-2 border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors'
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={!selectedVoice || !voiceRegeneration.allowed}
            className='px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
          >
            {!voiceRegeneration.allowed
              ? 'Regeneration Limit Reached'
              : 'Confirm Selection'}
          </button>
        </div>
      </div>
    </div>
  )

  // Render modal based on device type
  if (isDesktop) {
    return (
      <Dialog open={isOpen} onOpenChange={handleCancel}>
        <DialogContent className='min-w-4xl max-h-[90vh] h-[90vh] p-0 flex flex-col'>
          <DialogHeader className='px-6 py-4 border-b flex-shrink-0'>
            <DialogTitle className='text-xl font-semibold'>
              Choose a Voice
            </DialogTitle>
          </DialogHeader>
          <div className='px-6 py-4 flex-1 min-h-0'>{modalContent}</div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Drawer open={isOpen} onOpenChange={handleCancel}>
      <DrawerContent className='max-h-[90vh] h-[90vh] flex flex-col'>
        <DrawerHeader className='border-b flex-shrink-0'>
          <DrawerTitle className='flex items-center gap-2'>
            <Volume2 className='h-5 w-5' />
            Choose Voice
          </DrawerTitle>
        </DrawerHeader>
        <div className='p-4 flex-1 min-h-0'>{modalContent}</div>
      </DrawerContent>
    </Drawer>
  )
}
