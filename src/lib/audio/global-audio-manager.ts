/**
 * Global Audio Manager
 *
 * Centralized audio management system to prevent AudioContext accumulation
 * and ensure proper cleanup of audio elements across the application.
 *
 * Features:
 * - Single audio element reuse
 * - Automatic cleanup on component unmount
 * - Prevention of multiple AudioContext instances
 * - Memory leak prevention
 * - Proper error handling
 */

import { useEffect } from 'react'

type AudioInstance = {
  element: HTMLAudioElement
  cleanup: () => void
}

type AudioManagerOptions = {
  onPlay?: (url: string) => void
  onPause?: (url: string) => void
  onEnded?: (url: string) => void
  onError?: (error: Error, url: string) => void
  volume?: number
}

class GlobalAudioManager {
  private static instance: GlobalAudioManager
  private audioInstances: Map<string, AudioInstance> = new Map()
  private currentlyPlaying: string | null = null

  private constructor() {
    // Cleanup on page unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup()
      })
    }
  }

  static getInstance(): GlobalAudioManager {
    if (!GlobalAudioManager.instance) {
      GlobalAudioManager.instance = new GlobalAudioManager()
    }
    return GlobalAudioManager.instance
  }

  /**
   * Play audio with automatic cleanup and reuse
   */
  async play(
    instanceId: string,
    audioUrl: string,
    options: AudioManagerOptions = {}
  ): Promise<void> {
    try {
      // Stop any currently playing audio EXCEPT the one we're about to play
      this.audioInstances.forEach((instance, id) => {
        if (id !== instanceId && !instance.element.paused) {
          instance.element.pause()
        }
      })

      // Reset currently playing if it's not the instance we're about to play
      if (this.currentlyPlaying && this.currentlyPlaying !== instanceId) {
        this.currentlyPlaying = null
      }

      // Get or create audio instance
      let audioInstance = this.audioInstances.get(instanceId)

      // If this instance is already playing the same URL, don't interrupt it
      if (
        audioInstance &&
        audioInstance.element.src === audioUrl &&
        this.currentlyPlaying === instanceId
      ) {
        // Audio is already playing, just call onPlay callback
        options.onPlay?.(audioUrl)
        return
      }

      if (!audioInstance || audioInstance.element.src !== audioUrl) {
        // Clean up existing instance if URL changed
        if (audioInstance) {
          audioInstance.cleanup()
        }

        // Create new audio element
        const audio = new Audio()

        // Set up event listeners
        const cleanup = () => {
          audio.pause()
          audio.removeAttribute('src')
          audio.load() // Reset the audio element
          if (this.currentlyPlaying === instanceId) {
            this.currentlyPlaying = null
          }
        }

        audio.onended = () => {
          options.onEnded?.(audioUrl)
          cleanup()
        }

        audio.onpause = () => {
          options.onPause?.(audioUrl)
          if (this.currentlyPlaying === instanceId) {
            this.currentlyPlaying = null
          }
        }

        audio.onerror = () => {
          const error = new Error(`Audio loading failed: ${audioUrl}`)
          options.onError?.(error, audioUrl)
          cleanup()
        }

        // Set audio properties
        audio.volume = options.volume ?? 1
        audio.preload = 'none' // Don't preload to save memory
        audio.src = audioUrl

        audioInstance = {
          element: audio,
          cleanup,
        }

        this.audioInstances.set(instanceId, audioInstance)
      }

      // Add a small delay to ensure any previous pause operations complete
      await new Promise(resolve => setTimeout(resolve, 10))

      // Play the audio
      this.currentlyPlaying = instanceId
      await audioInstance.element.play()
      options.onPlay?.(audioUrl)
    } catch (error) {
      const audioError =
        error instanceof Error ? error : new Error('Audio playback failed')
      options.onError?.(audioError, audioUrl)
      console.error('GlobalAudioManager: Failed to play audio', audioError)
    }
  }

  /**
   * Pause specific audio instance
   */
  pause(instanceId: string): void {
    const audioInstance = this.audioInstances.get(instanceId)
    if (audioInstance) {
      audioInstance.element.pause()
    }
  }

  /**
   * Stop all audio playback
   */
  stopAll(): void {
    this.audioInstances.forEach(instance => {
      if (!instance.element.paused) {
        instance.element.pause()
      }
    })
    this.currentlyPlaying = null
  }

  /**
   * Check if specific instance is playing
   */
  isPlaying(instanceId: string): boolean {
    return this.currentlyPlaying === instanceId
  }

  /**
   * Get currently playing instance ID
   */
  getCurrentlyPlaying(): string | null {
    return this.currentlyPlaying
  }

  /**
   * Clean up specific audio instance
   */
  cleanupInstance(instanceId: string): void {
    const audioInstance = this.audioInstances.get(instanceId)
    if (audioInstance) {
      audioInstance.cleanup()
      this.audioInstances.delete(instanceId)
    }
  }

  /**
   * Clean up all audio instances
   */
  cleanup(): void {
    this.audioInstances.forEach(instance => {
      instance.cleanup()
    })
    this.audioInstances.clear()
    this.currentlyPlaying = null
  }

  /**
   * Get audio instance count for debugging
   */
  getInstanceCount(): number {
    return this.audioInstances.size
  }

  /**
   * Set volume for specific instance
   */
  setVolume(instanceId: string, volume: number): void {
    const audioInstance = this.audioInstances.get(instanceId)
    if (audioInstance) {
      audioInstance.element.volume = Math.max(0, Math.min(1, volume))
    }
  }
}

// Export singleton instance
export const globalAudioManager = GlobalAudioManager.getInstance()

// React hook for using the global audio manager
export function useGlobalAudio(instanceId: string) {
  const play = async (audioUrl: string, options?: AudioManagerOptions) => {
    return globalAudioManager.play(instanceId, audioUrl, options)
  }

  const pause = () => {
    globalAudioManager.pause(instanceId)
  }

  const isPlaying = () => {
    return globalAudioManager.isPlaying(instanceId)
  }

  const cleanup = () => {
    globalAudioManager.cleanupInstance(instanceId)
  }

  const setVolume = (volume: number) => {
    globalAudioManager.setVolume(instanceId, volume)
  }

  return {
    play,
    pause,
    isPlaying,
    cleanup,
    setVolume,
    stopAll: globalAudioManager.stopAll.bind(globalAudioManager),
  }
}

// Cleanup hook for React components
export function useAudioCleanup(instanceId: string) {
  useEffect(() => {
    return () => {
      globalAudioManager.cleanupInstance(instanceId)
    }
  }, [instanceId])
}
